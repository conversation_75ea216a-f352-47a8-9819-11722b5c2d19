/* -------------------------
   Fonts
-------------------------- */
@font-face {
  font-family: segoBd;
  src: url(./assets/fonts/segoe-ui-this/segoeuithibd.ttf);
}
@font-face {
  font-family: segoIs;
  src: url(./assets/fonts/segoe-ui-this/segoeuithis.ttf);
}

/* -------------------------
   Global Reset
-------------------------- */
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  cursor: default; /* Default cursor globally */
}

html,
body {
  height: 100%;
  width: 100%;
  font-family: segoIs;
}

:root {
  --brightness-level: 1;
}

body {
  filter: brightness(var(--brightness-level));
  transition: filter 0.3s ease-in-out;
}
/* -------------------------
   Main Layout
-------------------------- */
main {
  height: 100%;
  width: 100%;
}

/* -------------------------
   Desktop Background
-------------------------- */
.desktop {
  height: 100%;
  width: 100%;
  background-image: url("https://images.unsplash.com/photo-1697143493170-8cf836596b34?q=80&w=1632&auto=format&fit=crop");
  background-position: bottom;
  background-size: cover;
  background-repeat: no-repeat;
  position: relative;
}

.desktopIcons {
  position: absolute;
  top: 3%;
  left: 1.5%;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.iconCtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 5rem;
  cursor: pointer;
  color: white;
  text-shadow: 1px 1px 2px black;
}

.iconCtn img {
  width: 3rem;
  height: 3rem;
  object-fit: contain;
}

.iconCtn h4 {
  margin-top: 4px;
  font-size: 0.8rem;
  text-align: center;
}

/* -------------------------
   Taskbar Styling
-------------------------- */
.taskBar {
  position: absolute;
  bottom: 1px;
  background: #e4eff9;
  width: 100%;
  height: 6.6vh;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.2rem 1rem;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.2);
}

/* -------------------------
   Left Side of Taskbar (Weather)
-------------------------- */
.taskbarLeft {
  display: flex;
  align-items: center;
  gap: 10px;
  border-radius: 6px;
  padding: 2px 6px;
  cursor: pointer;
}
.cloud-icon {
  width: 2.2rem;
}
.weather h5 {
  font-size: 0.8rem;
}
.taskbarLeft:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* -------------------------
   Middle Section: Icons & Search
-------------------------- */
.taskbarMid {
  display: flex;
  align-items: center;
  margin-left: 6rem;
}
.taskIcons {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 10px;
}

/* Individual Icons */
.windowIcon,
.allIcons,
.searchIcon,
.wifiIcon,
.soundIcon,
.batteryIcon {
  transition: 0.2s ease-in-out;
  cursor: pointer;
}

/* Start Menu Icon */
.windowIcon {
  width: 2.6rem;
  padding: 8px;
  border-radius: 6px;
}

/* Other Application Icons */
.allIcons {
  width: 2.6rem;
  padding: 6px;
  border-radius: 8px;
}

/* Hover Effect Like Windows 11 */
.allIcons:hover,
.windowIcon:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Search Bar */
.searchBar {
  display: flex;
  align-items: center;
  background-color: #fff;
  border-radius: 50px;
  width: 10rem;
  padding: 0.99vh 0.6rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}
.searchIcon {
  width: 1rem;
}
input {
  border: none;
  background: transparent;
  margin-left: 6px;
  font-size: 0.9rem;
  cursor: text;
}
input:focus {
  outline: none;
}

/* -------------------------
   Right Side: System Info
-------------------------- */
.taskbarRight {
  display: flex;
  align-items: center;
  justify-content: center;
}

.upArrow {
  font-size: 1.6rem;
  cursor: pointer;
  padding: 2px 3px;
  border-radius: 6px;
}
.upArrow:hover,
.Lang h5:hover {
  background: rgba(0, 0, 0, 0.1);
}

.Lang h5 {
  padding: 5px;
  border-radius: 6px;
  font-size: 0.8rem;
  text-align: center;
  cursor: pointer;
}

.controlIcons {
  display: flex;
  align-items: center;
  border-radius: 6px;
}
.controlIcons img {
  width: 1.67rem;
  padding: 4px;
  cursor: pointer;
}
.controlIcons .batteryIcon {
  width: 1.9rem;
}
.controlIcons:hover {
  background: rgba(0, 0, 0, 0.1);
}

.dateTimeCtn {
  text-align: right;
  padding: 3px 6px;
  border-radius: 6px;
  cursor: pointer;
}
.dateTimeCtn h4 {
  font-size: 0.86rem;
}
.dateTimeCtn:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* -------------------------
   Start Window UI
-------------------------- */
.startWindow {
  position: absolute;
  bottom: 8%;
  left: 50%;
  transform: translate(-50%);
  background: #e4eff9;
  height: 80%;
  width: 50%;
  border-radius: 10px;
  z-index: 999;
}

.pinnedCtn {
  height: 45%;
  padding: 1.1rem 2.2rem;
  width: 100%;
}
.pinBar {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pinBar h3 {
  font-size: 0.9rem;
  font-weight: 600;
}

.allAppsIcon {
  padding: 4px 6px;
  border-radius: 6px;
  background: rgba(0, 0, 0, 0.1);
  cursor: pointer;
}
.allAppsIcon h3 {
  font-size: 0.8rem;
  cursor: pointer;
}
.allAppsIcon h3 i {
  margin-left: 3px;
  cursor: pointer;
}

.appsGrid {
  margin-top: 12px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 2.5rem;
  text-align: center;
  flex-wrap: wrap;
}
.appCtn {
  border-radius: 6px;
  padding: 4px 10px;
  cursor: pointer;
}
.appCtn:hover {
  background: rgba(0, 0, 0, 0.1);
}
.appCtn img {
  cursor: pointer;

  width: 3rem;
}
.appCtn h5 {
  font-size: 0.9rem;
}

.recommendedCtn {
  height: 45%;
  width: 100%;
  padding: 1.1rem 2.2rem;
}
.RecCtn {
  display: flex;
  align-items: center;
  margin-top: 15px;
  justify-content: start;
  flex-wrap: wrap;
}
.fileCtn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1vh;
  padding-right: 8rem;
  gap: 12px;
  border-radius: 6px;
  cursor: pointer;
}
.fileCtn:nth-child(even) {
  margin-left: 8vh;
}
.fileCtn:hover {
  background: rgba(0, 0, 0, 0.1);
}
.fileimg img {
  width: 2.6rem;
}
.fileTxt h4 {
  font-size: 1rem;
}
.fileTxt h5 {
  opacity: 0.7;
}

.profileBar {
  height: 10%;
  width: 100%;
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.1);
  justify-content: space-between;
  border-radius: 6px;
  padding: 1.1rem 2.2rem;
}
.prf {
  display: flex;
  align-items: center;
  gap: 10px;
}
.prf h2 {
  font-size: 1.1rem;
}
.prf img {
  width: 2rem;
  border-radius: 50px;
}

.powerIcon img {
  width: 1.4rem;
  cursor: pointer;
  transition: transform 0.2s ease-in-out;
}
.powerIcon img:active {
  transform: scale(0.7); /* Fix: add transform effect */
}

/* -------------------------
   App Window Style
-------------------------- */
.appWindow {
  position: absolute;
  top: 10%;
  left: 20%;
  width: 40%;
  height: 40%;
  background: #ffffff;
  border-radius: 10px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  display: none;
  flex-direction: column;
  z-index: 10;
  cursor: pointer;

}

.appHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #0067c0;
  color: white;
  padding: 0.5rem 1rem;
  font-weight: bold;
  user-select: none;
  cursor: move;
  cursor: pointer;
}

.appTitle {
  font-size: 1rem;
}

.windowControls button {
  margin-left: 5px;
  font-size: 1.3rem;
  padding: 4px 9px;
  border: none;
  background: none;
  color: white;
  cursor: pointer;
  border-radius: 6px;
  font-weight: bold;
  transition: 0.2s ease-in-out;
}
.windowControls button:hover {
  background: rgba(255, 255, 255, 0.2);
}
.windowControls button:nth-child(3):hover {
  background-color: red;
}

.appContent {
  padding: 1rem;
  font-size: 0.9rem;
}

.desktopIcons {
  position: absolute;
  top: 3%;
  left: 1.5%;
  display: flex;
  flex-direction: column;
  gap: 1.2rem;
}

.iconCtn {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 5rem;
  cursor: pointer;
  color: white;
  text-shadow: 1px 1px 2px black;
}

.iconCtn img {
  width: 3rem;
  height: 3rem;
  object-fit: contain;
}

.iconCtn h4 {
  margin-top: 4px;
  font-size: 0.8rem;
  text-align: center;
}

/* Quick Settings Panel - Windows 11 Style */
.glass-panel {
  background: rgba(32, 34, 40, 0.82);
  backdrop-filter: blur(22px) saturate(1.7);
  border-radius: 18px;
  box-shadow: 0 8px 32px #0007, 0 1.5px 0 #fff2 inset;
  border: 1.5px solid rgba(255,255,255,0.13);
  overflow: hidden;
}
.qs-media {
  display: flex;
  align-items: center;
  padding: 18px 18px 10px 18px;
  border-bottom: 1px solid #fff2;
  background: rgba(255,255,255,0.03);
}
.qs-media img {
  box-shadow: 0 2px 8px #0002;
}
.qs-btn {
  background: none;
  border: none;
  color: #eaf6ff;
  font-size: 1.2em;
  margin: 0 2px;
  cursor: pointer;
  border-radius: 6px;
  padding: 6px 8px;
  transition: background 0.15s, color 0.15s;
}
.qs-btn:hover {
  background: #2a7fff22;
  color: #fff;
}
.qs-toggle {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: rgba(255,255,255,0.09);
  border: none;
  border-radius: 12px;
  padding: 12px 0 6px 0;
  color: #eaf6ff;
  font-size: 0.98em;
  cursor: pointer;
  transition: background 0.18s, box-shadow 0.18s, color 0.18s;
  box-shadow: 0 2px 8px #0001;
  gap: 6px;
}
.qs-toggle.active, .qs-toggle:active {
  background: #2a7fffcc;
  color: #fff;
  box-shadow: 0 4px 16px #2a7fff33;
}
.qs-toggle img {
  width: 28px;
  height: 28px;
  margin-bottom: 2px;
  filter: drop-shadow(0 1px 2px #0002);
}
.qs-toggles {
  user-select: none;
}
.qs-sliders input[type=range] {
  accent-color: #2a7fff;
  width: 100%;
  margin: 0 8px;
  background: transparent;
}
.qs-sliders span, .qs-sliders label {
  color: #eaf6ff;
}
.qs-sliders {
  background: rgba(255,255,255,0.02);
  border-radius: 0 0 18px 18px;
}
.qs-sliders .qs-btn {
  padding: 4px 8px;
  font-size: 1.2em;
}
.qs-sliders .qs-btn i {
  font-size: 1.3em;
}
.qs-sliders .qs-btn:active {
  background: #2a7fff33;
}
.qs-sliders .qs-btn:focus {
  outline: 2px solid #2a7fff;
}
.qs-sliders .qs-btn {
  color: #eaf6ff;
}
.qs-sliders .qs-btn:hover {
  color: #fff;
}
.qs-sliders .qs-btn:active {
  color: #fff;
}
.qs-sliders .qs-btn:focus {
  color: #fff;
}
.qs-sliders .qs-btn {
  background: none;
  border: none;
}
.qs-sliders .qs-btn i {
  font-size: 1.3em;
}
.qs-sliders .qs-btn {
  padding: 0 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.qs-sliders .qs-btn {
  color: #eaf6ff;
}
.qs-sliders .qs-btn:hover {
  color: #fff;
}
.qs-sliders .qs-btn:active {
  color: #fff;
}
.qs-sliders .qs-btn:focus {
  color: #fff;
}
.qs-sliders .qs-btn {
  background: none;
  border: none;
}
.qs-sliders .qs-btn i {
  font-size: 1.3em;
}
.qs-sliders .qs-btn {
  padding: 0 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.qs-sliders .qs-btn {
  color: #eaf6ff;
}
.qs-sliders .qs-btn:hover {
  color: #fff;
}
.qs-sliders .qs-btn:active {
  color: #fff;
}
.qs-sliders .qs-btn:focus {
  color: #fff;
}
.qs-sliders .qs-btn {
  background: none;
  border: none;
}
.qs-sliders .qs-btn i {
  font-size: 1.3em;
}
.qs-sliders .qs-btn {
  padding: 0 6px;
  display: flex;
  align-items: center;
  cursor: pointer;
}
.qs-sliders .qs-btn {
  color: #eaf6ff;
}
.qs-sliders .qs-btn:hover {
  color: #fff;
}
.qs-sliders .qs-btn:active {
  color: #fff;
}
.qs-sliders .qs-btn:focus {
  color: #fff;
}
@media (max-width: 500px) {
  #quickSettingsPanel {
    width: 98vw !important;
    right: 1vw !important;
    left: 1vw !important;
    min-width: unset;
    max-width: unset;
  }
}

/* Ensure pointer cursor for all interactive elements */
button,
.qs-btn,
.qs-toggle,
input[type="range"],
.controlIcons,
.taskIcons img,
.windowIcon,
.allIcons,
.searchIcon,
.taskbarLeft,
.Lang h5,
.upArrow,
.dateTimeCtn,
.appCtn,
.fileCtn,
.powerIcon img,
.profileBar,
#startBtn {
  cursor: pointer !important;
}

/* Remove pointer from non-interactive elements if inherited */
.desktop,
.desktopIcons,
.iconCtn h4,
.fileTxt h4,
.fileTxt h5,
.weather h5,
.startWindow,
.pinnedCtn,
.recommendedCtn,
.appsGrid,
.RecCtn {
  cursor: default !important;
}

.window {
            width: 100%;
            height: 100vh;
            background: #ffffff;
            border: 1px solid #e1e1e1;
            display: flex;
            flex-direction: column;
        }

        /* Title Bar */
        .title-bar {
            height: 32px;
            background: #ffffff;
            display: flex;
            align-items: center;
            padding: 0 12px;
            border-bottom: 1px solid #e1e1e1;
            -webkit-app-region: drag;
        }

        .title-bar .icon {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23106ebe"><path d="M4 6h16v2H4V6zm0 5h16v2H4v-2zm0 5h16v2H4v-2z"/></svg>') no-repeat center;
            background-size: contain;
        }

        .title-bar .title {
            flex: 1;
            font-size: 12px;
            font-weight: 400;
        }

        .title-bar .controls {
            display: flex;
            -webkit-app-region: no-drag;
        }

        .title-bar .control-btn {
            width: 46px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 10px;
            transition: background-color 0.1s;
        }

        .title-bar .control-btn:hover {
            background: #e5e5e5;
        }

        .title-bar .control-btn.close:hover {
            background: #e81123;
            color: white;
        }

        /* Menu Bar */
        .menu-bar {
            height: 40px;
            background: #fafafa;
            display: flex;
            align-items: center;
            padding: 0 12px;
            border-bottom: 1px solid #e1e1e1;
        }

        .menu-item {
            padding: 8px 16px;
            font-size: 13px;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.1s;
        }

        .menu-item:hover {
            background: #e5e5e5;
        }

        .menu-item.active {
            background: #ffffff;
            border: 1px solid #d1d1d1;
            border-bottom: 1px solid #ffffff;
            margin-bottom: -1px;
        }

        /* Ribbon */
        .ribbon {
            height: 120px;
            background: #ffffff;
            border-bottom: 1px solid #e1e1e1;
            display: flex;
            padding: 8px 12px;
            gap: 24px;
        }

        .ribbon-group {
            display: flex;
            flex-direction: column;
            min-width: 80px;
        }

        .ribbon-group-title {
            font-size: 11px;
            color: #606060;
            text-align: center;
            margin-top: 8px;
            border-top: 1px solid #e1e1e1;
            padding-top: 4px;
        }

        .ribbon-buttons {
            display: flex;
            gap: 4px;
            flex-wrap: wrap;
        }

        .ribbon-btn {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.1s;
            font-size: 11px;
            min-width: 60px;
        }

        .ribbon-btn:hover {
            background: #e5e5e5;
        }

        .ribbon-btn-icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .icon-copy { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm0 16H8V7h11v14z"/></svg>'); }
        .icon-paste { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M19 2h-4.18C14.4.84 13.3 0 12 0c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm7 18H5V4h2v3h10V4h2v16z"/></svg>'); }
        .icon-cut { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M9.64 7.64c.23-.5.36-1.05.36-1.64 0-2.21-1.79-4-4-4S2 3.79 2 6s1.79 4 4 4c.59 0 1.14-.13 1.64-.36L10 12l-2.36 2.36C7.14 14.13 6.59 14 6 14c-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4c0-.59-.13-1.14-.36-1.64L12 14l7 7h3v-1L9.64 7.64zM6 8c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm0 12c-1.1 0-2-.89-2-2s.9-2 2-2 2 .89 2 2-.9 2-2 2zm6-7.5c-.28 0-.5-.22-.5-.5s.22-.5.5-.5.5.22.5.5-.22.5-.5.5zM12 6l8.5 8.5L22 13L12 3z"/></svg>'); }
        .icon-new-folder { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M20 6h-2l-2-2H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V8c0-1.1-.9-2-2-2zm-1 8h-3v3h-2v-3h-3v-2h3V9h2v3h3v2z"/></svg>'); }
        .icon-delete { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M6 19c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2V7H6v12zM19 4h-3.5l-1-1h-5l-1 1H5v2h14V4z"/></svg>'); }
        .icon-rename { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/></svg>'); }

        /* Navigation Bar */
        .nav-bar {
            height: 40px;
            background: #ffffff;
            display: flex;
            align-items: center;
            padding: 0 12px;
            border-bottom: 1px solid #e1e1e1;
            gap: 8px;
        }

        .nav-btn {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 4px;
            transition: background-color 0.1s;
            font-size: 16px;
            color: #323130;
        }

        .nav-btn:hover {
            background: #e5e5e5;
        }

        .nav-btn:disabled {
            color: #a19f9d;
            cursor: not-allowed;
        }

        .address-bar {
            flex: 1;
            height: 32px;
            border: 1px solid #d1d1d1;
            border-radius: 4px;
            padding: 0 12px;
            display: flex;
            align-items: center;
            background: #ffffff;
            font-size: 13px;
        }

        .search-box {
            width: 240px;
            height: 32px;
            border: 1px solid #d1d1d1;
            border-radius: 4px;
            padding: 0 12px;
            font-size: 13px;
            background: #ffffff;
        }

        .search-box:focus {
            outline: none;
            border-color: #106ebe;
        }

        /* Main Content */
        .main-content {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        /* Sidebar */
        .sidebar {
            width: 240px;
            background: #fafafa;
            border-right: 1px solid #e1e1e1;
            display: flex;
            flex-direction: column;
            padding: 8px 0;
        }

        .sidebar-section {
            margin-bottom: 16px;
        }

        .sidebar-item {
            display: flex;
            align-items: center;
            padding: 8px 16px;
            cursor: pointer;
            transition: background-color 0.1s;
            font-size: 13px;
        }

        .sidebar-item:hover {
            background: #e5e5e5;
        }

        .sidebar-item.active {
            background: #e3f2fd;
            color: #106ebe;
        }

        .sidebar-item-icon {
            width: 16px;
            height: 16px;
            margin-right: 12px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .icon-this-pc { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23106ebe"><path d="M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z"/></svg>'); }
        .icon-desktop { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M21 2H3c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h7l-2 3v1h8v-1l-2-3h7c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm0 12H3V4h18v10z"/></svg>'); }
        .icon-documents { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>'); }
        .icon-downloads { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg>'); }
        .icon-music { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/></svg>'); }
        .icon-pictures { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>'); }
        .icon-videos { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M18 3v2h-2V3H8v2H6V3H4v18h2v-2h2v2h8v-2h2v2h2V3h-2zM8 17H6v-2h2v2zm0-4H6v-2h2v2zm0-4H6V7h2v2zm10 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V7h2v2z"/></svg>'); }

        /* File Area */
        .file-area {
            flex: 1;
            background: #ffffff;
            padding: 16px;
            overflow-y: auto;
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
            gap: 16px;
            padding: 8px;
        }

        .file-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.1s;
            text-align: center;
        }

        .file-item:hover {
            background: #e5e5e5;
        }

        .file-item.selected {
            background: #e3f2fd;
            border: 1px solid #106ebe;
        }

        .file-icon {
            width: 48px;
            height: 48px;
            margin-bottom: 8px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .file-name {
            font-size: 12px;
            word-wrap: break-word;
            max-width: 100%;
        }

        .icon-folder { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffd700"><path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/></svg>'); }
        .icon-file { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23323130"><path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>'); }
        .icon-image { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ff6b6b"><path d="M21 19V5c0-1.1-.9-2-2-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2zM8.5 13.5l2.5 3.01L14.5 12l4.5 6H5l3.5-4.5z"/></svg>'); }
        .icon-video { background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2351cf66"><path d="M18 3v2h-2V3H8v2H6V3H4v18h2v-2h2v2h8v-2h2v2h2V3h-2zM8 17H6v-2h2v2zm0-4H6v-2h2v2zm0-4H6V7h2v2zm10 8h-2v-2h2v2zm0-4h-2v-2h2v2zm0-4h-2V7h2v2z"/></svg>'); }

        /* Status Bar */
        .status-bar {
            height: 24px;
            background: #fafafa;
            border-top: 1px solid #e1e1e1;
            display: flex;
            align-items: center;
            padding: 0 12px;
            font-size: 11px;
            color: #606060;
        }

        .status-text {
            flex: 1;
        }

        .view-options {
            display: flex;
            gap: 4px;
        }

        .view-btn {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 2px;
            transition: background-color 0.1s;
        }

        .view-btn:hover {
            background: #e5e5e5;
        }

        .view-btn.active {
            background: #106ebe;
            color: white;
        }

        /* Scrollbar */
        ::-webkit-scrollbar {
            width: 12px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 6px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }